2025-07-02 16:17:09 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:17:09 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:17:09 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:17:09 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:17:09 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:17:09 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:17:09 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:17:09 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:17:09 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:17:09 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:17:13 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:17:13 | WARNING  | __main__:install_dependencies:103 - ✗ OpenAI API支持 不可用 (缺少 openai)
2025-07-02 16:17:13 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:17:13 | WARNING  | __main__:install_dependencies:103 - ✗ 语音转文字功能 不可用 (缺少 whisper)
2025-07-02 16:17:13 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:17:13 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:17:13 | INFO     | __main__:main:130 - 环境检查完成，退出
2025-07-02 16:17:29 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:17:29 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:17:29 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:17:29 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:17:29 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:17:29 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:17:29 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:17:29 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:17:29 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:17:29 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:17:30 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:17:30 | INFO     | __main__:install_dependencies:101 - ✓ OpenAI API支持 可用
2025-07-02 16:17:30 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:17:30 | WARNING  | __main__:install_dependencies:103 - ✗ 语音转文字功能 不可用 (缺少 whisper)
2025-07-02 16:17:30 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:17:30 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:17:30 | INFO     | __main__:main:134 - 启动服务器: http://0.0.0.0:8000
2025-07-02 16:17:30 | INFO     | __main__:main:135 - API文档: http://0.0.0.0:8000/docs
2025-07-02 16:17:30 | INFO     | __main__:main:136 - 重载模式: False
2025-07-02 16:17:30 | INFO     | __main__:main:137 - 工作进程: 1
2025-07-02 16:17:31 | ERROR    | __main__:main:152 - 服务器启动失败: Attribute name 'metadata' is reserved when using the Declarative API.
2025-07-02 16:18:56 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:18:56 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:18:56 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:18:56 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:18:56 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:18:56 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:18:56 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:18:56 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:18:56 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:18:56 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:18:56 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:18:56 | INFO     | __main__:install_dependencies:101 - ✓ OpenAI API支持 可用
2025-07-02 16:18:56 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:18:56 | WARNING  | __main__:install_dependencies:103 - ✗ 语音转文字功能 不可用 (缺少 whisper)
2025-07-02 16:18:56 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:18:56 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:18:56 | INFO     | __main__:main:134 - 启动服务器: http://0.0.0.0:8000
2025-07-02 16:18:56 | INFO     | __main__:main:135 - API文档: http://0.0.0.0:8000/docs
2025-07-02 16:18:56 | INFO     | __main__:main:136 - 重载模式: False
2025-07-02 16:18:56 | INFO     | __main__:main:137 - 工作进程: 1
2025-07-02 16:18:57 | ERROR    | __main__:main:152 - 服务器启动失败: `regex` is removed. use `pattern` instead

For further information visit https://errors.pydantic.dev/2.11/u/removed-kwargs
2025-07-02 16:20:29 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:20:29 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:20:29 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:20:29 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:20:29 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:20:29 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:20:29 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:20:29 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:20:29 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:20:29 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:20:29 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:20:29 | INFO     | __main__:install_dependencies:101 - ✓ OpenAI API支持 可用
2025-07-02 16:20:29 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:20:29 | WARNING  | __main__:install_dependencies:103 - ✗ 语音转文字功能 不可用 (缺少 whisper)
2025-07-02 16:20:29 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:20:29 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:20:29 | INFO     | __main__:main:134 - 启动服务器: http://0.0.0.0:8000
2025-07-02 16:20:29 | INFO     | __main__:main:135 - API文档: http://0.0.0.0:8000/docs
2025-07-02 16:20:29 | INFO     | __main__:main:136 - 重载模式: False
2025-07-02 16:20:29 | INFO     | __main__:main:137 - 工作进程: 1
2025-07-02 16:20:30 | ERROR    | __main__:main:152 - 服务器启动失败: No module named 'sentence_transformers'
2025-07-02 16:21:19 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:21:19 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:21:19 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:21:19 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:21:19 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:21:19 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:21:19 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:21:19 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:21:19 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:21:19 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:21:19 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:21:20 | INFO     | __main__:install_dependencies:101 - ✓ OpenAI API支持 可用
2025-07-02 16:21:20 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:21:20 | WARNING  | __main__:install_dependencies:103 - ✗ 语音转文字功能 不可用 (缺少 whisper)
2025-07-02 16:21:20 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:21:20 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:21:20 | INFO     | __main__:main:134 - 启动服务器: http://0.0.0.0:8000
2025-07-02 16:21:20 | INFO     | __main__:main:135 - API文档: http://0.0.0.0:8000/docs
2025-07-02 16:21:20 | INFO     | __main__:main:136 - 重载模式: False
2025-07-02 16:21:20 | INFO     | __main__:main:137 - 工作进程: 1
2025-07-02 16:21:50 | ERROR    | __main__:main:152 - 服务器启动失败: No module named 'cv2'
2025-07-02 16:22:10 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:22:10 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:22:10 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:22:10 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:22:10 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:22:10 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:22:10 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:22:10 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:22:10 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:22:10 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:22:11 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:22:11 | INFO     | __main__:install_dependencies:101 - ✓ OpenAI API支持 可用
2025-07-02 16:22:11 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:22:11 | WARNING  | __main__:install_dependencies:103 - ✗ 语音转文字功能 不可用 (缺少 whisper)
2025-07-02 16:22:11 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:22:11 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:22:11 | INFO     | __main__:main:134 - 启动服务器: http://0.0.0.0:8000
2025-07-02 16:22:11 | INFO     | __main__:main:135 - API文档: http://0.0.0.0:8000/docs
2025-07-02 16:22:11 | INFO     | __main__:main:136 - 重载模式: False
2025-07-02 16:22:11 | INFO     | __main__:main:137 - 工作进程: 1
2025-07-02 16:22:24 | ERROR    | __main__:main:152 - 服务器启动失败: No module named 'whisper'
2025-07-02 16:22:50 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:22:50 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:22:50 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:22:50 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:22:50 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:22:50 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:22:50 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:22:50 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:22:50 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:22:50 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:22:51 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:22:51 | INFO     | __main__:install_dependencies:101 - ✓ OpenAI API支持 可用
2025-07-02 16:22:51 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:22:56 | INFO     | __main__:install_dependencies:101 - ✓ 语音转文字功能 可用
2025-07-02 16:22:57 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:22:57 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:22:57 | INFO     | __main__:main:134 - 启动服务器: http://0.0.0.0:8000
2025-07-02 16:22:57 | INFO     | __main__:main:135 - API文档: http://0.0.0.0:8000/docs
2025-07-02 16:22:57 | INFO     | __main__:main:136 - 重载模式: False
2025-07-02 16:22:57 | INFO     | __main__:main:137 - 工作进程: 1
2025-07-02 16:23:00 | ERROR    | __main__:main:152 - 服务器启动失败: No module named 'paddleocr'
2025-07-02 16:23:43 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:23:43 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:23:43 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:23:43 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:23:43 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:23:43 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:23:43 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:23:43 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:23:43 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:23:43 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:23:44 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:23:44 | INFO     | __main__:install_dependencies:101 - ✓ OpenAI API支持 可用
2025-07-02 16:23:44 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:23:45 | INFO     | __main__:install_dependencies:101 - ✓ 语音转文字功能 可用
2025-07-02 16:23:45 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:23:45 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:23:45 | INFO     | __main__:main:134 - 启动服务器: http://0.0.0.0:8000
2025-07-02 16:23:45 | INFO     | __main__:main:135 - API文档: http://0.0.0.0:8000/docs
2025-07-02 16:23:45 | INFO     | __main__:main:136 - 重载模式: False
2025-07-02 16:23:45 | INFO     | __main__:main:137 - 工作进程: 1
2025-07-02 16:24:08 | ERROR    | __main__:main:152 - 服务器启动失败: cannot import name 'init_database' from 'core.database' (/Users/<USER>/Desktop/RAG_try/core/database.py)
2025-07-02 16:26:05 | INFO     | __main__:main:121 - ==================================================
2025-07-02 16:26:05 | INFO     | __main__:main:122 - RAG知识库系统启动
2025-07-02 16:26:05 | INFO     | __main__:main:123 - ==================================================
2025-07-02 16:26:05 | INFO     | __main__:check_environment:42 - 检查环境配置...
2025-07-02 16:26:05 | INFO     | __main__:check_environment:69 - 确保目录存在: ./logs
2025-07-02 16:26:05 | INFO     | __main__:check_environment:69 - 确保目录存在: ./uploads
2025-07-02 16:26:05 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data
2025-07-02 16:26:05 | INFO     | __main__:check_environment:69 - 确保目录存在: ./data/vector_db
2025-07-02 16:26:05 | INFO     | __main__:check_environment:71 - 环境检查完成
2025-07-02 16:26:05 | INFO     | __main__:install_dependencies:76 - 检查Python依赖...
2025-07-02 16:26:05 | INFO     | __main__:install_dependencies:83 - 核心依赖已安装
2025-07-02 16:26:06 | INFO     | __main__:install_dependencies:101 - ✓ OpenAI API支持 可用
2025-07-02 16:26:06 | WARNING  | __main__:install_dependencies:103 - ✗ OCR功能 不可用 (缺少 paddlepaddle)
2025-07-02 16:26:07 | INFO     | __main__:install_dependencies:101 - ✓ 语音转文字功能 可用
2025-07-02 16:26:07 | INFO     | __main__:install_dependencies:101 - ✓ 中文分词 可用
2025-07-02 16:26:07 | WARNING  | __main__:install_dependencies:103 - ✗ 缓存功能 不可用 (缺少 redis)
2025-07-02 16:26:07 | INFO     | __main__:main:134 - 启动服务器: http://0.0.0.0:8000
2025-07-02 16:26:07 | INFO     | __main__:main:135 - API文档: http://0.0.0.0:8000/docs
2025-07-02 16:26:07 | INFO     | __main__:main:136 - 重载模式: False
2025-07-02 16:26:07 | INFO     | __main__:main:137 - 工作进程: 1
2025-07-02 16:26:11 | INFO     | main:lifespan:21 - 正在启动RAG系统...
2025-07-02 16:26:11 | INFO     | core.database:init_db:31 - 数据库初始化完成
2025-07-02 16:26:12 | INFO     | core.database:create_default_admin:60 - 创建默认管理员用户: admin/admin123
2025-07-02 16:26:12 | INFO     | core.embeddings:__init__:140 - 使用OpenAI向量化模型
2025-07-02 16:26:12 | INFO     | core.vector_store:__init__:35 - 创建新集合: rag_documents
2025-07-02 16:26:12 | INFO     | main:lifespan:35 - RAG系统启动完成
2025-07-02 16:27:20 | ERROR    | main:health_check:84 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-02 16:27:20 | INFO     | core.embeddings:__init__:140 - 使用OpenAI向量化模型
2025-07-02 16:27:21 | ERROR    | core.embeddings:embed_query:70 - OpenAI查询向量化失败: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-02 16:27:21 | ERROR    | main:health_check:105 - Embedding模型检查失败: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-02 16:31:06 | ERROR    | core.embeddings:embed_query:70 - OpenAI查询向量化失败: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-02 16:31:06 | ERROR    | core.vector_store:similarity_search:112 - 相似度搜索失败: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-02 16:31:06 | ERROR    | core.retrieval:retrieve:177 - 混合检索失败: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
